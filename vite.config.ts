
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8081, // Use 8081 directly since 8080 is occupied
    strictPort: false, // Allow automatic port switching if needed
    hmr: {
      port: 8081, // Explicitly set HMR WebSocket port
      host: 'localhost', // Ensure WebSocket uses localhost
      clientPort: 8081, // Ensure client connects to correct port
    },
    watch: {
      usePolling: false, // Use native file watching for better performance
    },
    proxy: {
      '/api': {
        target: 'http://localhost:4001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  define: {
    // Ensure Vite client uses correct port
    __VITE_HMR_PORT__: 8081,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-router-dom', '@supabase/supabase-js']
        }
      }
    }
  },
}));
