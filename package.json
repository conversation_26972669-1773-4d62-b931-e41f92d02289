{"name": "educonnect-monorepo", "private": true, "type": "module", "workspaces": ["shared", "mobile", "web"], "scripts": {"dev": "npm run dev:web", "dev:web": "npm run dev --workspace=web", "dev:mobile": "npm run start --workspace=mobile", "build": "npm run build:web", "build:web": "npm run build --workspace=web", "build:mobile": "npm run build --workspace=mobile", "build:dev": "npm run build --workspace=web", "build:shared": "npm run build --workspace=shared", "preview": "npm run preview --workspace=web", "postinstall": "patch-package"}, "dependencies": {"@radix-ui/react-tooltip": "^1.2.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.11.0", "i18next": "^25.3.2", "lovable-tagger": "^1.1.8", "next-themes": "^0.4.6", "react-i18next": "^15.6.0", "shared": "file:./shared", "vite": "^7.0.5", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"patch-package": "^8.0.0"}}